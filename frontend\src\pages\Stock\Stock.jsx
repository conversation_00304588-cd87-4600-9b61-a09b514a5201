/**
 * 📊 STOCK PAGE - Controle de Estoque
 * ===================================
 * 
 * Página que exibe todos os produtos com informações de estoque
 * e permite gerenciar movimentações.
 */

import React, { useState, useEffect } from 'react';
import axios from 'axios';

const Stock = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filter, setFilter] = useState('all'); // all, low_stock, out_of_stock

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('🔄 Carregando produtos...');
      
      const response = await axios.get('/api/products');
      
      if (response.data.success) {
        setProducts(response.data.data || []);
        console.log('✅ Produtos carregados:', response.data.data?.length || 0);
      } else {
        throw new Error('Erro ao carregar produtos');
      }
    } catch (err) {
      console.error('❌ Erro ao carregar produtos:', err);
      setError('Erro ao carregar produtos. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  const getFilteredProducts = () => {
    if (!products) return [];
    
    switch (filter) {
      case 'low_stock':
        return products.filter(p => p.stock_quantity <= p.min_stock && p.stock_quantity > 0);
      case 'out_of_stock':
        return products.filter(p => p.stock_quantity === 0);
      default:
        return products;
    }
  };

  const getStockStatus = (product) => {
    if (product.stock_quantity === 0) return 'out_of_stock';
    if (product.stock_quantity <= product.min_stock) return 'low_stock';
    return 'in_stock';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'out_of_stock': return '#ff4757';
      case 'low_stock': return '#ffa502';
      default: return '#2ed573';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'out_of_stock': return 'Sem Estoque';
      case 'low_stock': return 'Estoque Baixo';
      default: return 'Em Estoque';
    }
  };

  const filteredProducts = getFilteredProducts();

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '400px',
        flexDirection: 'column',
        gap: '20px'
      }}>
        <div style={{
          width: '50px',
          height: '50px',
          border: '4px solid #f3f3f3',
          borderTop: '4px solid #3498db',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
        <p>Carregando produtos...</p>
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ 
        padding: '40px', 
        textAlign: 'center',
        color: '#e74c3c'
      }}>
        <h3>❌ {error}</h3>
        <button 
          onClick={loadProducts}
          style={{
            marginTop: '20px',
            padding: '10px 20px',
            backgroundColor: '#3498db',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          Tentar Novamente
        </button>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      {/* Header */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '30px',
        flexWrap: 'wrap',
        gap: '20px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
          <button 
            onClick={() => window.history.back()}
            style={{
              padding: '8px 16px',
              backgroundColor: '#95a5a6',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '5px'
            }}
          >
            ← Voltar
          </button>
          <div>
            <h1 style={{ margin: 0, display: 'flex', alignItems: 'center', gap: '10px' }}>
              📦 Controle de Estoque
            </h1>
            <p style={{ margin: '5px 0 0 0', color: '#7f8c8d' }}>
              {filteredProducts.length} produto(s) encontrado(s)
            </p>
          </div>
        </div>

        <button
          onClick={() => window.location.href = '/products/add'}
          style={{
            padding: '12px 24px',
            backgroundColor: '#27ae60',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer',
            fontWeight: 'bold'
          }}
        >
          + Nova Movimentação
        </button>
      </div>

      {/* Filtros */}
      <div style={{ 
        display: 'flex', 
        gap: '10px', 
        marginBottom: '20px',
        flexWrap: 'wrap'
      }}>
        {[
          { key: 'all', label: 'Todos', count: products.length },
          { key: 'low_stock', label: 'Estoque Baixo', count: products.filter(p => p.stock_quantity <= p.min_stock && p.stock_quantity > 0).length },
          { key: 'out_of_stock', label: 'Sem Estoque', count: products.filter(p => p.stock_quantity === 0).length }
        ].map(({ key, label, count }) => (
          <button
            key={key}
            onClick={() => setFilter(key)}
            style={{
              padding: '8px 16px',
              backgroundColor: filter === key ? '#3498db' : '#ecf0f1',
              color: filter === key ? 'white' : '#2c3e50',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer',
              fontWeight: filter === key ? 'bold' : 'normal'
            }}
          >
            {label} ({count})
          </button>
        ))}
      </div>

      {/* Lista de Produtos */}
      {filteredProducts.length === 0 ? (
        <div style={{ 
          textAlign: 'center', 
          padding: '60px 20px',
          backgroundColor: '#f8f9fa',
          borderRadius: '10px',
          border: '2px dashed #dee2e6'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>📦</div>
          <h3 style={{ color: '#6c757d', marginBottom: '10px' }}>
            {filter === 'all' ? 'Nenhum produto cadastrado' : 
             filter === 'low_stock' ? 'Nenhum produto com estoque baixo' :
             'Nenhum produto sem estoque'}
          </h3>
          <p style={{ color: '#6c757d' }}>
            {filter === 'all' ? 'Cadastre produtos primeiro na área de Produtos' : 
             'Todos os produtos estão com estoque adequado'}
          </p>
        </div>
      ) : (
        <div style={{ 
          display: 'grid', 
          gap: '15px'
        }}>
          {filteredProducts.map((product) => {
            const status = getStockStatus(product);
            const statusColor = getStatusColor(status);
            const statusText = getStatusText(status);

            return (
              <div
                key={product.id}
                style={{
                  backgroundColor: 'white',
                  border: '1px solid #e1e8ed',
                  borderRadius: '10px',
                  padding: '20px',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  cursor: 'pointer'
                }}
                onMouseEnter={(e) => {
                  e.target.style.transform = 'translateY(-2px)';
                  e.target.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
                }}
              >
                <div style={{ 
                  display: 'grid', 
                  gridTemplateColumns: 'auto 1fr auto auto auto',
                  gap: '20px',
                  alignItems: 'center'
                }}>
                  {/* Nome */}
                  <div>
                    <h3 style={{ margin: 0, color: '#2c3e50' }}>{product.name}</h3>
                    {product.description && (
                      <p style={{ margin: '5px 0 0 0', color: '#7f8c8d', fontSize: '14px' }}>
                        {product.description}
                      </p>
                    )}
                  </div>

                  {/* Categoria */}
                  <div style={{ textAlign: 'center' }}>
                    <span style={{ color: '#7f8c8d', fontSize: '14px' }}>Categoria</span>
                    <p style={{ margin: '5px 0 0 0', fontWeight: 'bold' }}>-</p>
                  </div>

                  {/* Preço */}
                  <div style={{ textAlign: 'center' }}>
                    <span style={{ color: '#7f8c8d', fontSize: '14px' }}>Preço</span>
                    <p style={{ margin: '5px 0 0 0', fontWeight: 'bold', color: '#27ae60' }}>
                      R$ {parseFloat(product.price || 0).toFixed(2)}
                    </p>
                  </div>

                  {/* Estoque */}
                  <div style={{ textAlign: 'center' }}>
                    <span style={{ color: '#7f8c8d', fontSize: '14px' }}>Estoque</span>
                    <div style={{ 
                      margin: '5px 0 0 0',
                      padding: '5px 10px',
                      backgroundColor: statusColor,
                      color: 'white',
                      borderRadius: '15px',
                      fontSize: '12px',
                      fontWeight: 'bold'
                    }}>
                      {product.stock_quantity || 0}
                    </div>
                    <p style={{ margin: '5px 0 0 0', fontSize: '12px', color: '#7f8c8d' }}>
                      Min: {product.min_stock || 0}
                    </p>
                  </div>

                  {/* Ações */}
                  <div style={{ display: 'flex', gap: '10px' }}>
                    <button
                      style={{
                        padding: '8px 12px',
                        backgroundColor: '#3498db',
                        color: 'white',
                        border: 'none',
                        borderRadius: '5px',
                        cursor: 'pointer',
                        fontSize: '12px'
                      }}
                    >
                      Editar
                    </button>
                    <button
                      style={{
                        padding: '8px 12px',
                        backgroundColor: '#e74c3c',
                        color: 'white',
                        border: 'none',
                        borderRadius: '5px',
                        cursor: 'pointer',
                        fontSize: '12px'
                      }}
                    >
                      Excluir
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default Stock;

import React, { useState, useEffect } from 'react';

const TestStock = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadProducts = async () => {
      try {
        console.log('🔄 Testando carregamento de produtos...');
        
        const response = await fetch('http://10.19.208.52:3001/api/products');
        const data = await response.json();
        
        console.log('📦 Resposta da API:', data);
        
        if (data.success) {
          setProducts(data.data || []);
          console.log('✅ Produtos carregados:', data.data?.length || 0);
        } else {
          throw new Error('Erro na resposta da API');
        }
      } catch (err) {
        console.error('❌ Erro:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    loadProducts();
  }, []);

  if (loading) {
    return <div style={{ padding: '20px' }}>⏳ Carregando...</div>;
  }

  if (error) {
    return <div style={{ padding: '20px', color: 'red' }}>❌ Erro: {error}</div>;
  }

  return (
    <div style={{ padding: '20px' }}>
      <h1>🧪 Teste de Carregamento de Produtos</h1>
      <p>Total de produtos: <strong>{products.length}</strong></p>
      
      {products.length > 0 && (
        <div>
          <h2>Primeiros 5 produtos:</h2>
          {products.slice(0, 5).map(product => (
            <div key={product.id} style={{ 
              border: '1px solid #ccc', 
              margin: '10px 0', 
              padding: '10px',
              borderRadius: '5px'
            }}>
              <h3>{product.name}</h3>
              <p>Preço: R$ {product.price}</p>
              <p>Estoque: {product.stock_quantity}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default TestStock;

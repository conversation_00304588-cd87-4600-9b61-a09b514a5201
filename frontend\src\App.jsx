/**
 * 📱 FRONTEND - Interface THINK-Estoque
 * =====================================
 * 
 * Interface responsiva mobile-first que se comunica APENAS com o backend.
 * 
 * IMPORTANTE:
 * - NÃO contém lógica de negócio
 * - Apenas coleta dados do usuário e exibe respostas
 * - Todas as decisões vêm do THINK API via backend
 * - Foco em UX/UI e responsividade
 */

import React, { Suspense } from 'react';
import { HashRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'react-hot-toast';
import { motion, AnimatePresence } from 'framer-motion';

// Hooks e contextos
import { useAuthStore } from './stores/authStore';
import { useThemeStore } from './stores/themeStore';
import { SocketProvider } from './contexts/SocketContext';

// Componentes de layout
import Layout from './components/Layout/Layout';
import LoadingSpinner from './components/UI/LoadingSpinner';
import ErrorBoundary from './components/ErrorBoundary/ErrorBoundary';

// Páginas (lazy loading para performance)
const Login = React.lazy(() => import('./pages/Auth/Login'));
const Dashboard = React.lazy(() => import('./pages/Dashboard/Dashboard'));
const Products = React.lazy(() => import('./pages/Products/Products'));
const ProductAdd = React.lazy(() => import('./pages/Products/ProductAdd'));
const ProductDetail = React.lazy(() => import('./pages/Products/ProductDetail'));
const Stock = React.lazy(() => import('./pages/Stock/Stock'));
const Reports = React.lazy(() => import('./pages/Reports/Reports'));
const Camera = React.lazy(() => import('./pages/Camera/Camera'));
const Settings = React.lazy(() => import('./pages/Settings/Settings'));
const NotFound = React.lazy(() => import('./pages/NotFound/NotFound'));

// Configuração do React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      staleTime: 5 * 60 * 1000, // 5 minutos
      cacheTime: 10 * 60 * 1000, // 10 minutos
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 1,
    },
  },
});

// ============================================================================
// 🎨 COMPONENTE PRINCIPAL
// ============================================================================

function App() {
  const { isAuthenticated, isLoading } = useAuthStore();
  const { theme } = useThemeStore();

  // Loading inicial da aplicação
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <LoadingSpinner size="lg" />
          <motion.h2 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="mt-4 text-xl font-semibold text-gray-700 dark:text-gray-300"
          >
            🧠 Carregando THINK-Estoque...
          </motion.h2>
        </motion.div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <SocketProvider>
          <div className={`min-h-screen ${theme === 'dark' ? 'dark' : ''}`}>
            <Router>
              <AnimatePresence mode="wait">
                <Routes>
                  {/* ============================================ */}
                  {/* 🔐 ROTAS DE AUTENTICAÇÃO */}
                  {/* ============================================ */}
                  {!isAuthenticated ? (
                    <Route
                      path="*"
                      element={
                        <Suspense fallback={<LoadingSpinner />}>
                          <motion.div
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            exit={{ opacity: 0, x: 20 }}
                            transition={{ duration: 0.3 }}
                          >
                            <Login />
                          </motion.div>
                        </Suspense>
                      }
                    />
                  ) : (
                    /* ============================================ */
                    /* 🏠 ROTAS PROTEGIDAS */
                    /* ============================================ */
                    <>
                      <Route path="/" element={<Layout />}>
                        {/* Dashboard Principal */}
                        <Route
                          index
                          element={
                            <Suspense fallback={<LoadingSpinner />}>
                              <PageTransition>
                                <Dashboard />
                              </PageTransition>
                            </Suspense>
                          }
                        />

                        {/* ================================ */}
                        {/* 📦 PRODUTOS */}
                        {/* ================================ */}
                        <Route
                          path="products"
                          element={
                            <Suspense fallback={<LoadingSpinner />}>
                              <PageTransition>
                                <Products />
                              </PageTransition>
                            </Suspense>
                          }
                        />
                        <Route
                          path="products/add"
                          element={
                            <Suspense fallback={<LoadingSpinner />}>
                              <PageTransition>
                                <ProductAdd />
                              </PageTransition>
                            </Suspense>
                          }
                        />
                        <Route
                          path="products/:id"
                          element={
                            <Suspense fallback={<LoadingSpinner />}>
                              <PageTransition>
                                <ProductDetail />
                              </PageTransition>
                            </Suspense>
                          }
                        />

                        {/* ================================ */}
                        {/* 📊 ESTOQUE */}
                        {/* ================================ */}
                        <Route
                          path="stock"
                          element={
                            <Suspense fallback={<LoadingSpinner />}>
                              <PageTransition>
                                <Stock />
                              </PageTransition>
                            </Suspense>
                          }
                        />

                        {/* ================================ */}
                        {/* 📈 RELATÓRIOS */}
                        {/* ================================ */}
                        <Route
                          path="reports"
                          element={
                            <Suspense fallback={<LoadingSpinner />}>
                              <PageTransition>
                                <Reports />
                              </PageTransition>
                            </Suspense>
                          }
                        />

                        {/* ================================ */}
                        {/* 📸 CAMERA */}
                        {/* ================================ */}
                        <Route
                          path="camera"
                          element={
                            <Suspense fallback={<LoadingSpinner />}>
                              <PageTransition>
                                <Camera />
                              </PageTransition>
                            </Suspense>
                          }
                        />

                        {/* ================================ */}
                        {/* ⚙️ CONFIGURAÇÕES */}
                        {/* ================================ */}
                        <Route
                          path="settings"
                          element={
                            <Suspense fallback={<LoadingSpinner />}>
                              <PageTransition>
                                <Settings />
                              </PageTransition>
                            </Suspense>
                          }
                        />

                        {/* ================================ */}
                        {/* 404 - PÁGINA NÃO ENCONTRADA */}
                        {/* ================================ */}
                        <Route
                          path="*"
                          element={
                            <Suspense fallback={<LoadingSpinner />}>
                              <PageTransition>
                                <NotFound />
                              </PageTransition>
                            </Suspense>
                          }
                        />
                      </Route>
                    </>
                  )}
                </Routes>
              </AnimatePresence>
            </Router>

            {/* ============================================ */}
            {/* 🍞 NOTIFICAÇÕES TOAST */}
            {/* ============================================ */}
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: theme === 'dark' ? '#374151' : '#ffffff',
                  color: theme === 'dark' ? '#f3f4f6' : '#111827',
                  border: `1px solid ${theme === 'dark' ? '#4b5563' : '#e5e7eb'}`,
                  borderRadius: '12px',
                  boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
                },
                success: {
                  iconTheme: {
                    primary: '#10b981',
                    secondary: '#ffffff',
                  },
                },
                error: {
                  iconTheme: {
                    primary: '#ef4444',
                    secondary: '#ffffff',
                  },
                },
              }}
            />
          </div>
        </SocketProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

// ============================================================================
// 🎭 COMPONENTE DE TRANSIÇÃO DE PÁGINAS
// ============================================================================

const PageTransition = ({ children }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{
        duration: 0.3,
        ease: "easeInOut"
      }}
      className="w-full"
    >
      {children}
    </motion.div>
  );
};

export default App;
